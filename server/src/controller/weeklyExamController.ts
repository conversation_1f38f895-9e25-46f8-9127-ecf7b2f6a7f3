import { getWeeklyLeaderboard } from '../services/weeklyExamServices';
import { Request, Response } from 'express';

export const getWeeklyLeaderboardController = async (req: Request, res: Response): Promise<any> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const leaderboard = await getWeeklyLeaderboard(page, limit);
    return res.status(200).json(leaderboard);
  } catch (error) {
    console.error('Error fetching weekly leaderboard:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};