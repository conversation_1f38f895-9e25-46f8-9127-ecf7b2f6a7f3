import { Request, Response } from "express";
import * as weeklyExamStreakServices from "../services/mockExamWeeklyStreakService";

export const getWeeklyExamStreakController = async (req: Request, res: Response): Promise<any> => {
  try {
    const studentId = req.params.studentId;
    const streakData = await weeklyExamStreakServices.getWeeklyStreak(studentId);
    return res.status(200).json({
      success: true,
      data: streakData,
    });
  } catch (error: any) {
    res.status(400).json({ success: false, error: error.message || error });
  }
};

export const updateWeeklyExamStreakController = async (req: Request, res: Response): Promise<any> => {
  try {
    const studentId = req.params.studentId;
    const streakData = await weeklyExamStreakServices.updateWeeklyStreak(studentId);
    return res.status(200).json({
      success: true,
      data: streakData,
    });
  } catch (error: any) {
    res.status(400).json({ success: false, error: error.message || error });
  }
};