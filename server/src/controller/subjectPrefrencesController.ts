import { Request, Response } from "express";
import * as subjectPrefrenceServices from "../services/subjectPrefrenceServices";

export const addSubjectPrefrenceController = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const data = req.body;
    const subjectPrefrenceData =
      await subjectPrefrenceServices.addSubjectPrefrence(data);
    res.status(201).json({ success: true, data: subjectPrefrenceData });
  } catch (error: any) {
    res.status(400).json({ success: false, error: error.message || error });
  }
};

export const getSubjectPrefrencesController = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const examId = parseInt(req.params.examId, 10);
    const subjectPrefrenceData =
      await subjectPrefrenceServices.getSubjectPrefrence(examId);
    return res.status(200).json({
      success: true,
      data: subjectPrefrenceData,
    });
  } catch (error: any) {
    res.status(400).json({ success: false, error: error.message || error });
  }
};

export const deleteSubjectPrefrences = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const { id } = req.params;
    const success = await subjectPrefrenceServices.deleteSubjectPrefrence(id);
    if (!success) {
      res
        .status(404)
        .json({ success: false, error: "SubjectPreference not found" });
      return;
    }
    res.status(204).send();
  } catch (error: any) {
    res.status(400).json({ success: false, error: error.message || error });
  }
};
