import { Request, Response } from 'express';
import { getLeaderboard,getPreviousDayTop3 } from '../services/mockExamLeaderBoardService';

export const getetLeaderboardController = async (req: Request, res: Response): Promise<any> => {  
  try {
    const timeframe = req.params.timeframe as any;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const {
      firstName,
      lastName,
      email,
      score,
      coins,
      streak,
      startDate,
      endDate,
    } = req.query;
    if (!['today', 'weekly', 'all-time'].includes(timeframe)) {
      return res.status(400).json({ error: 'Invalid timeframe' });
    }

    const filters: {
      firstName?: string;
      lastName?: string;
      email?: string;
      score?: string;
      coins?: string;
      streak?: string;
      startDate?: string;
      endDate?: string;
    } = {};

    if (firstName && typeof firstName === 'string') filters.firstName = firstName.trim();
    if (lastName && typeof lastName === 'string') filters.lastName = lastName.trim();
    if (email && typeof email === 'string') filters.email = email.trim();
    if (score && typeof score === 'string') filters.score = score.trim();
    if (coins && typeof coins === 'string') filters.coins = coins.trim();
    if (streak && typeof streak === 'string') filters.streak = streak.trim();
    
    if (startDate && typeof startDate === 'string') filters.startDate = startDate.trim();
        if (endDate && typeof endDate === 'string') filters.endDate = endDate.trim();

    const leaderboard = await getLeaderboard(timeframe, page, limit, filters);

    return res.status(200).json({
      message: 'Leaderboard retrieved successfully',
      ...leaderboard,
    });
  } catch (error) {
    console.error('Error fetching leaderboard:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

export const getPreviousDayLeaderboardController = async (req: Request, res: Response): Promise<any> => {
  try {
    const top3 = await getPreviousDayTop3();
    return res.status(200).json({ data: top3 });
  } catch (error) {
    console.error("Error fetching previous day leaderboard:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};