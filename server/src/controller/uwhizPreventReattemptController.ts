import { Request, Response } from "express";
import { hasUserAttemptedExam,countTerminations } from "../services/uwhizpreventReattemptServices";

export const checkExamAttempt = async (req:Request, res:Response):Promise<any> => {
  try {
    const { studentId, examId } = req.query;

    if (!studentId || !examId) {
      return res.status(400).json(false);
    }

    const hasAttempted = await hasUserAttemptedExam(studentId, examId);

    return res.status(200).json(hasAttempted);
  } catch (error) {
    return res.status(500).json(false);
  }
};

export const countTerminationsController = async (req:Request, res:Response):Promise<any> => {
  try {
    const { studentId, examId } = req.query;

    if (!studentId || !examId) {
      return res.status(400).json(false);
    }

    const terminationCount = await countTerminations(studentId, examId);

    return res.status(200).json(terminationCount);
  } catch (error) {
    return res.status(500).json(false);
  }
};