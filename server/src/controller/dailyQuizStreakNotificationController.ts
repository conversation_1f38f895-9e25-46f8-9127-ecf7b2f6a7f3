import { Request, Response } from "express";
import { processDailyQuizStreakNotifications, getStreakStatistics } from "../services/dailyQuizStreakNotificationService";
import axios from 'axios';

/**
 * Manually trigger daily quiz streak notifications (for testing)
 */
export const triggerDailyQuizStreakNotifications = async (req: Request, res: Response): Promise<any> => {
  try {
    const result = await processDailyQuizStreakNotifications();

    return res.status(200).json({
      success: true,
      message: 'Daily quiz streak notifications processed successfully',
      data: result
    });
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      error: error.message || 'Internal server error'
    });
  }
};

/**
 * Get streak statistics for monitoring
 */
export const getStreakStatisticsController = async (req: Request, res: Response): Promise<any> => {
  try {
    const stats = await getStreakStatistics();

    return res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      error: error.message || 'Internal server error'
    });
  }
};

/**
 * Get notification system status
 */
export const getNotificationSystemStatus = async (req: Request, res: Response): Promise<any> => {
  try {
    const status = {
      systemActive: true,
      cronSchedules: [
        { time: '9:00 AM IST', cron: '0 9 * * *', description: 'Morning notification' },
        { time: '6:00 PM IST', cron: '0 18 * * *', description: 'Evening notification' }
      ],
      description: 'Sends daily quiz streak notifications to all students twice daily via uest-app server',
      lastRunTime: null,
      nextRunTimes: ['Next 9:00 AM IST', 'Next 6:00 PM IST'],
      uestAppServerUrl: process.env.UEST_BACKEND_URL || 'http://localhost:4005'
    };

    return res.status(200).json({
      success: true,
      data: status
    });
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      error: error.message || 'Internal server error'
    });
  }
};

