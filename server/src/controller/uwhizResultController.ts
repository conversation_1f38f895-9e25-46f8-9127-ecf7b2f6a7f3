import { calculateRankings } from '../services/uwhizResultServices';
import { Request,Response } from 'express';

export const getRankingsByExamId = async (req: Request, res: Response): Promise<void> => {
  try {
    const { examId } = req.params;
    const {
      page = 1,
      limit = 10,
      firstName,
      lastName,
      email,
      contact,
      score,
      rank,
    } = req.query;

    if (!examId || isNaN(Number(examId))) {
      res.status(400).json({
        message: 'Valid examId is required',
      });
      return;
    }

    const filters: {
      firstName?: string;
      lastName?: string;
      email?: string;
      score?: string;
      contact?:string;
      rank?: string;
    } = {};

    if (firstName) filters.firstName = firstName as string;
    if (lastName) filters.lastName = lastName as string;
    if (email) filters.email = email as string;
    if (score) filters.score = score as string;
    if (contact) filters.contact = contact as string; 
    if (rank) filters.rank = rank as string;

    const result = await calculateRankings(
      Number(examId),
      parseInt(page as string),
      parseInt(limit as string),
      filters
    );

    res.status(200).json({
      message: 'Rankings retrieved successfully',
      ...result,
    });
  } catch (error: any) {
    res.status(500).json({
      message: 'Failed to get rankings',
      error: error.message,
    });
  }
};