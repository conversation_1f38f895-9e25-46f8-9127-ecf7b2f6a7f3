import { Request, Response } from "express";
import * as uwhizSaveAnswer from "../services/uwhizSaveAnswerServices";
import { SaveExamAnswer } from "@prisma/client";

export const saveExamAnswerController = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const data = req.body as SaveExamAnswer;

    const saveExamAnswerData = await uwhizSaveAnswer.SaveExamAnswerService(data);
    res.status(201).json(saveExamAnswerData);
  } catch (error: any) {
    res
      .status(400)
      .json({ message: "Error saving answer", error: error.message });
  }
};