import prisma from "../config/prismaClient";

export const getCurrentStreak = async (studentId: string) => {
  try {
    const streakRecord = await prisma.mockExamStreak.findFirst({
      where: { studentId },
      orderBy: { lastAttempt: "desc" },
    });

    if (!streakRecord) {
      return { success: true, streak: 0, lastAttempt: null };
    }

    return {
      success: true,
      streak: streakRecord.streakCount,
      lastAttempt: streakRecord.lastAttempt,
    };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};

export const updateStreak = async (studentId: string) => {
  try {
    const currentDate = new Date();
    const yesterday = new Date(currentDate);
    yesterday.setDate(currentDate.getDate() - 1);

    const getDate = (date: Date) => {
      return new Date(date.getFullYear(), date.getMonth(), date.getDate());
    };

    const today = getDate(currentDate);
    const yesterdayDate = getDate(yesterday);

    const streakRecord = await prisma.mockExamStreak.findFirst({
      where: { studentId },
      orderBy: { lastAttempt: "desc" },
    });

    let newStreakCount = 1;

    if (streakRecord) {
      const lastAttemptDate = getDate(new Date(streakRecord.lastAttempt));

      if (lastAttemptDate.getTime() === today.getTime()) {
        return {
          success: true,
          streak: streakRecord.streakCount,
          message: "Exam already attempted today. Streak unchanged.",
        };
      }
      else if (lastAttemptDate.getTime() === yesterdayDate.getTime()) {
        newStreakCount = streakRecord.streakCount + 1;
      }
      else {
        newStreakCount = 1;
      }
    }

    const updatedStreak = await prisma.mockExamStreak.upsert({
      where: { id: streakRecord?.id || "" },
      update: {
        streakCount: newStreakCount,
        lastAttempt: currentDate,
        updatedAt: currentDate,
      },
      create: {
        studentId,
        streakCount: newStreakCount,
        lastAttempt: currentDate,
      },
    });

    return {
      success: true,
      streak: updatedStreak.streakCount,
      lastAttempt: updatedStreak.lastAttempt,
    };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};