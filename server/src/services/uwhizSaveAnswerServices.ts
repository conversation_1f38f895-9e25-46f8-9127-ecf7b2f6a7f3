import prisma from "../config/prismaClient";

export const SaveExamAnswerService = async (data: {
  studentId: string;
  examId: number;
  questionId: string;
  selectedAns: string | null;
}) => {
  const examExists = await prisma.exam.findUnique({
    where: { id: data.examId },
  });
  if (!examExists) {
    throw new Error("Exam not found");
  }

  const questionExists = await prisma.questionBank.findUnique({
    where: { id: data.questionId },
  });
  if (!questionExists) {
    throw new Error("Question not found");
  }

  // Upsert the answer
  return prisma.saveExamAnswer.upsert({
    where: {
      studentId_examId_questionId: {
        studentId: data.studentId,
        examId: data.examId,
        questionId: data.questionId,
      },
    },
    update: {
      selectedAns: data.selectedAns,
    },
    create: {
      studentId: data.studentId,
      examId: data.examId,
      questionId: data.questionId,
      selectedAns: data.selectedAns,
    },
  });
};