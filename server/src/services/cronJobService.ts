import * as cron from "node-cron";
import { processDailyQuizStreakNotifications } from './dailyQuizStreakNotificationService';

/**
 * Common cron job service for managing scheduled tasks
 */
export class CronJobService {
  private static instance: CronJobService;
  private jobs: Map<string, cron.ScheduledTask> = new Map();

  private constructor() {}

  public static getInstance(): CronJobService {
    if (!CronJobService.instance) {
      CronJobService.instance = new CronJobService();
    }
    return CronJobService.instance;
  }

  /**
   * Schedule daily quiz streak notifications
   */
  public scheduleDailyQuizNotifications(): void {
    const morningJob = cron.schedule(
      "0 9 * * *",
      async () => {
        try {
          await processDailyQuizStreakNotifications();
        } catch (error) {
          // Silent error handling
        }
      },
      {
        timezone: "Asia/Kolkata"
      }
    );

    const eveningJob = cron.schedule(
      "0 18 * * *",
      async () => {
        try {
          await processDailyQuizStreakNotifications();
        } catch (error) {
          // Silent error handling
        }
      },
      {
        timezone: "Asia/Kolkata"
      }
    );

    this.jobs.set('morning-quiz-notifications', morningJob);
    this.jobs.set('evening-quiz-notifications', eveningJob);

    morningJob.start();
    eveningJob.start();
  }

  /**
   * Get status of all scheduled jobs
   */
  public getJobsStatus(): { [key: string]: { active: boolean } } {
    const status: { [key: string]: { active: boolean } } = {};

    this.jobs.forEach((_, name) => {
      status[name] = {
        active: true // Job exists in our map and is scheduled
      };
    });

    return status;
  }

  /**
   * Stop a specific job
   */
  public stopJob(jobName: string): boolean {
    const job = this.jobs.get(jobName);
    if (job) {
      job.stop();
      return true;
    }
    return false;
  }

  public startJob(jobName: string): boolean {
    const job = this.jobs.get(jobName);
    if (job) {
      job.start();
      return true;
    }
    return false;
  }

  public stopAllJobs(): void {
    this.jobs.forEach((job) => {
      job.stop();
    });
  }

  /**
   * Get list of all job names
   */
  public getJobNames(): string[] {
    return Array.from(this.jobs.keys());
  }
}

export default CronJobService;
