import { PrismaClient } from "@prisma/client";
import axios from "axios";

const prisma = new PrismaClient();

export async function getWeeklyLeaderboard(
  page: number = 1,
  limit: number = 10
): Promise<{
  data: any[];
  total: number;
}> {
  const today = new Date();
  const currentDay = today.getDay(); 
  
  let mostRecentSunday = new Date();
  
  if (currentDay === 0) {
    mostRecentSunday = new Date();
  } else {
    mostRecentSunday.setDate(today.getDate() - currentDay);
  }
  
  mostRecentSunday.setHours(0, 0, 0, 0);

  const endOfSunday = new Date(mostRecentSunday);
  endOfSunday.setHours(23, 59, 59, 999);

  const skip = (page - 1) * limit;

  const whereClause = {
    isWeekly: true,
    createdAt: {
      gte: mostRecentSunday,
      lte: endOfSunday,
    },
  };

  const total = await prisma.mockExamResult.groupBy({
    by: ['studentId'],
    where: whereClause,
  }).then((results) => results.length);

  const groupedResults = await prisma.mockExamResult.groupBy({
    by: ["studentId"],
    _sum: {
      score: true,
    },
    where: whereClause,
    orderBy: [
      {
        _sum: {
          score: "desc", 
        },
      },
    ],
    skip,
    take: limit,
  });

  const allStudentIds = groupedResults.map((r) => r.studentId);

 
  let studentDetails: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    profile: {
      photo: string | null;
    };
  }[] = [];

  try {
    const response = await axios.post(
      `${process.env.UEST_BACKEND_URL}/uwhiz-terminated-students`,
      { applicantIds: allStudentIds },
      { headers: { "Content-Type": "application/json" } }
    );
    studentDetails = response.data;
  } catch (err) {
    console.error("Failed to fetch student details:", err);
  }

  const validStudentIds = studentDetails.map((s) => s.id);
  const filteredResults = groupedResults.filter((r) =>
    validStudentIds.includes(r.studentId)
  );

  const leaderboard = filteredResults.map((result, index) => {
    const student = studentDetails.find((s) => s.id === result.studentId);

    return {
      rank: skip + index + 1,
      studentId: result.studentId,
      studentName: `${student?.firstName || ''} ${student?.lastName || ''}`.trim() || 'Unknown',
      firstName: student?.firstName,
      lastName: student?.lastName,
      score: result._sum.score ?? 0, 
      profilePhoto: student?.profile?.photo,
    };
  });

  return {
    data: leaderboard,
    total,
  };
}