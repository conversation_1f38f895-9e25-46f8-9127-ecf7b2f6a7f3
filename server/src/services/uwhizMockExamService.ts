import { MEDIUM, PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();
import NodeCache from 'node-cache';
const quizCache = new NodeCache({ stdTTL: 86400, checkperiod: 60 });

export const getMockQuestionsForStudent = async (
  studentId: string, 
   medium: MEDIUM,
  isWeekly: boolean
) => {
  await prisma.mockExamTermination.deleteMany({ where: { studentId } });

  const mockExamQuestions = await prisma.mockExamQuestionBank.findMany({
    select: {
      id: true,
      question: true,
      optionOne: true,
      optionTwo: true,
      optionThree: true,
      optionFour: true,
      correctAnswer: true,
      questioncategory: true
    },
    where: {
      medium,
      isWeeklyExam: isWeekly
    }
  });

  const questionsByCategory = mockExamQuestions.reduce((acc, question) => {
    const category = question.questioncategory?.trim().toLowerCase();
    if (category) {
      if (!acc[category]) acc[category] = [];
      acc[category].push(question);
    }
    return acc;
  }, {} as Record<string, typeof mockExamQuestions>);

  const categories = Object.keys(questionsByCategory);
  let selectedQuestions: typeof mockExamQuestions = [];

  const limit = isWeekly ? 25 : 10;

  for (const category of categories) {
    const qList = questionsByCategory[category];
    const randomQ = qList[Math.floor(Math.random() * qList.length)];
    if (randomQ) selectedQuestions.push(randomQ);
  }

  const alreadySelectedIds = new Set(selectedQuestions.map(q => q.id));
  const remainingSlots = limit - selectedQuestions.length;

  if (remainingSlots > 0) {
    const remainingQuestions = mockExamQuestions.filter(
      q => !alreadySelectedIds.has(q.id)
    );

    const additionalRandom = remainingQuestions
      .sort(() => Math.random() - 0.5)
      .slice(0, remainingSlots);

    selectedQuestions.push(...additionalRandom);
  }

  if (selectedQuestions.length > limit) {
    selectedQuestions = selectedQuestions
      .sort(() => Math.random() - 0.5)
      .slice(0, limit);
  }

  const finalQuestions = selectedQuestions.sort(() => Math.random() - 0.5);
  return finalQuestions;
};
