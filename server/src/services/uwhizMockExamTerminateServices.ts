import prisma from "../config/prismaClient";

export const addQuizTerminationServices = async (studentId:string,reason:string) =>{
    const quizTerminationData = await prisma.mockExamTermination.create({
        data:{
            reason,
            studentId
        }
    });
    return quizTerminationData;
}

export const countTerminations = async (studentId: any) => {
 try{
    const terminationCount = await prisma.mockExamTermination.count({
      where: {
        studentId: studentId,
      },
    });

    return terminationCount;
  } catch (error) {
    console.error('Error checking exam attempt:', error);
    return false; 
  }
}
export const getStudentTerminationLogs = async (studentId: string) => {
    try {
        const terminationLogs = await prisma.mockExamTermination.findMany({
            where: {
                studentId: studentId
            },
            select: {
                id: true,
                reason: true,
                studentId: true,
                createdAt: true,
            },
            orderBy: {
                createdAt: 'desc' 
            }
        });
        
        return terminationLogs;
    } catch (error) {
        console.error('Error fetching student termination logs:', error);
        return [];
    }
}