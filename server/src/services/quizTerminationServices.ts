import axios from "axios";
import prisma from "../config/prismaClient";

export const addQuizTerminationServices = async (examId:number,applicantId:string,reason:string) =>{
    const quizTerminationData = await prisma.quizTemination.create({
        data:{
            examId,
            reason,
            applicantId
        }
    });
    return quizTerminationData;
}
export const getQuizTerminationServices = async (examId:any, page:any, limit:any) => {
  const skip = (page - 1) * limit;

  const exam = await prisma.exam.findUnique({ where: { id: parseInt(examId) } });
  if (!exam) throw new Error('Exam not found');

  const applications = await prisma.quizTemination.findMany({
    where: { examId: parseInt(examId) },
    skip,
    take: limit,
    orderBy: { createdAt: 'desc' },
  });

  const uniqueApplicantIds = [...new Set(applications.map(app => app.applicantId))];
  const response = await axios.post(`${process.env.UEST_BACKEND_URL}/uwhiz-terminated-students`, {
    applicantIds: uniqueApplicantIds,
  });
  const applicantDataMap = new Map();
  response.data.forEach((applicant: any) => {
    applicantDataMap.set(applicant.id, applicant); 
  });

  const dataWithCreatedAt = applications.map((termination) => ({
    ...applicantDataMap.get(termination.applicantId),
    createdAt: termination.createdAt,
    reason: termination.reason,
    terminationId: termination.id,
  }));

  return {
    total: await prisma.quizTemination.count({ where: { examId: parseInt(examId) } }),
    page,
    limit,
    data: dataWithCreatedAt,
  }
}
export const getStudentTerminationLogs = async (examId: number, applicantId: string, page: number = 1, limit: number = 10) => {
  const skip = (page - 1) * limit;
  const exam = await prisma.exam.findUnique({ where: { id: examId } });
  if (!exam) throw new Error('Exam not found');
  const terminationLogs = await prisma.quizTemination.findMany({
    where: {
      examId: examId,
      applicantId: applicantId
    },
    skip,
    take: limit,
    orderBy: { createdAt: 'desc' },
  });
  const totalCount = await prisma.quizTemination.count({
    where: {
      examId: examId,
      applicantId: applicantId
    }
  });
  return {
    applicantId,
    totalTerminations: totalCount,
    terminationLogs: terminationLogs.map(log => ({
      id: log.id,
      reason: log.reason,
      createdAt: log.createdAt,
    }))
  };
}