import prisma from "../config/prismaClient";
import axios from 'axios';

interface StudentStreakData {
  studentId: string;
  streakCount: number;
  lastAttempt: string | null;
}

/**
 * Fetch all students with their current streak data from uwhiz database
 */
export const getAllStudentsWithStreaks = async (): Promise<StudentStreakData[]> => {
  try {
    const uestAppUrl = process.env.UEST_BACKEND_URL || 'http://localhost:4005';
    let studentIds: string[] = [];

    try {
      const response = await axios.get(`${uestAppUrl}/api/v1/admin/students/ids`, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'X-Source': 'uwhiz-server'
        }
      });

      if (response.data && response.data.success && response.data.data && response.data.data.students) {
        studentIds = response.data.data.students.map((s: any) => s.id);
      }
    } catch (apiError) {
      const studentsFromResults = await prisma.mockExamResult.findMany({
        select: {
          studentId: true,
        },
        distinct: ['studentId']
      });

      studentIds = studentsFromResults.map(s => s.studentId);
    }

    if (studentIds.length === 0) {
      return [];
    }

    const streakData: StudentStreakData[] = [];

    for (const studentId of studentIds) {
      try {
        const streakRecord = await prisma.mockExamStreak.findFirst({
          where: { studentId },
          orderBy: { lastAttempt: "desc" },
        });

        streakData.push({
          studentId,
          streakCount: streakRecord?.streakCount || 0,
          lastAttempt: streakRecord?.lastAttempt ? streakRecord.lastAttempt.toISOString() : null
        });
      } catch (error) {
        streakData.push({
          studentId,
          streakCount: 0,
          lastAttempt: null
        });
      }
    }

    return streakData;
  } catch (error) {
    throw error;
  }
};

/**
 * Send streak notification data to uest-app server
 */
export const sendStreakNotificationToUestApp = async (studentsWithStreaks: StudentStreakData[]) => {
  try {
    const uestAppUrl = process.env.UEST_BACKEND_URL || 'http://localhost:4005';

    const response = await axios.post(`${uestAppUrl}/api/v1/admin/daily-quiz-streak-notifications/process-streaks`, {
      studentsWithStreaks,
      timestamp: new Date().toISOString(),
      source: 'uwhiz-server'
    }, {
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'X-Source': 'uwhiz-server'
      }
    });

    if (response.data && response.data.success) {
      return {
        success: true,
        notificationCount: response.data.notificationCount,
        errors: response.data.errors || []
      };
    } else {
      throw new Error('Invalid response from uest-app server');
    }
  } catch (error) {
    console.error("Error sending streak notification:", error);
  }
};

/**
 * Main function to process daily quiz streak notifications
 * This will be called by the cron job
 */
export const processDailyQuizStreakNotifications = async () => {
  try {
    const studentsWithStreaks = await getAllStudentsWithStreaks();

    if (studentsWithStreaks.length === 0) {
      return {
        success: true,
        message: 'No students found',
        notificationCount: 0,
        errors: []
      };
    }

    const result = await sendStreakNotificationToUestApp(studentsWithStreaks);

    return result;
  } catch (error) {
    console.error(error);
  }
};

/**
 * Get statistics about student streaks (for monitoring/debugging)
 */
export const getStreakStatistics = async () => {
  try {
    const studentsWithStreaks = await getAllStudentsWithStreaks();

    const stats = {
      totalStudents: studentsWithStreaks.length,
      studentsWithStreak: studentsWithStreaks.filter(s => s.streakCount > 0).length,
      studentsWithoutStreak: studentsWithStreaks.filter(s => s.streakCount === 0).length,
      averageStreak: studentsWithStreaks.length > 0
        ? studentsWithStreaks.reduce((sum, s) => sum + s.streakCount, 0) / studentsWithStreaks.length
        : 0,
      maxStreak: studentsWithStreaks.length > 0
        ? Math.max(...studentsWithStreaks.map(s => s.streakCount))
        : 0,
      streakDistribution: {
        '0': studentsWithStreaks.filter(s => s.streakCount === 0).length,
        '1-7': studentsWithStreaks.filter(s => s.streakCount >= 1 && s.streakCount <= 7).length,
        '8-30': studentsWithStreaks.filter(s => s.streakCount >= 8 && s.streakCount <= 30).length,
        '30+': studentsWithStreaks.filter(s => s.streakCount > 30).length,
      }
    };
    return stats;
  } catch (error) {
    console.error('Error getting streak statistics:', error);
    throw error;
  }
};