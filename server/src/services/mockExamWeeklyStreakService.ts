import prisma from "../config/prismaClient";

export const getWeeklyStreak = async (studentId: string) => {
  try {
    const streakRecord = await prisma.weeklyExamStreak.findFirst({
      where: { studentId },
      orderBy: { lastAttempt: "desc" },
    });

    if (!streakRecord) {
      return { success: true, streak: 0, lastAttempt: null };
    }

    return {
      success: true,
      streak: streakRecord.streakCount,
      lastAttempt: streakRecord.lastAttempt,
    };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};

export const updateWeeklyStreak = async (studentId: string) => {
  try {
    const currentDate = new Date();
    const currentSunday = new Date(currentDate);
    currentSunday.setDate(currentDate.getDate() - currentDate.getDay());
    currentSunday.setHours(0, 0, 0, 0);

    const streakRecord = await prisma.weeklyExamStreak.findFirst({
      where: { studentId },
      orderBy: { lastAttempt: "desc" },
    });

    let newStreakCount = 1;

    if (streakRecord) {
      const lastAttemptDate = new Date(streakRecord.lastAttempt);
      const lastAttemptSunday = new Date(lastAttemptDate);
      lastAttemptSunday.setDate(lastAttemptDate.getDate() - lastAttemptDate.getDay());
      lastAttemptSunday.setHours(0, 0, 0, 0);

      const daysDiff = Math.round(
        (currentSunday.getTime() - lastAttemptSunday.getTime()) / (1000 * 3600 * 24)
      );

      if (daysDiff === 0) {
        return {
          success: true,
          streak: streakRecord.streakCount,
          message: "Weekly exam already attempted this week. Streak unchanged.",
        };
      } else if (daysDiff === 7) {
        newStreakCount = streakRecord.streakCount + 1;
      } else {
        newStreakCount = 1;
      }
    }

    const updatedStreak = await prisma.weeklyExamStreak.upsert({
      where: { id: streakRecord?.id || "" },
      update: {
        streakCount: newStreakCount,
        lastAttempt: currentDate,
        updatedAt: currentDate,
      },
      create: {
        studentId,
        streakCount: newStreakCount,
        lastAttempt: currentDate,
      },
    });

    return {
      success: true,
      streak: updatedStreak.streakCount,
      lastAttempt: updatedStreak.lastAttempt,
    };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};