import { PrismaClient } from "@prisma/client";
import axios from "axios";
import NodeCache from "node-cache";

const prisma = new PrismaClient();
const cache = new NodeCache({ stdTTL: 86400 });

export async function getLeaderboard(
  timeframe: string,
  page: number = 1,
  limit: number = 10,
  filters: {
    firstName?: string;
    lastName?: string;
    email?: string;
    score?: string;
    coins?: string;
    streak?: string;
    startDate?: string;
    endDate?: string;
  } = {}
): Promise<{
  data: any[];
  total: number;
    reactions: {
    [studentId: string]: {
      reaction: string | null;
      counts: {
        thumbsup: number;
        whistle: number;
        party: number;
        clap: number;
      };
    };
  };
}> {

  let startDate: Date | undefined;
  let endDate: Date | undefined;

  if (timeframe === "today") {
    startDate = new Date();
    startDate.setHours(0, 0, 0, 0);
  } else if (timeframe === "weekly") {
    startDate = new Date();
    startDate.setDate(startDate.getDate() - startDate.getDay());
    startDate.setHours(0, 0, 0, 0);
  }

  if (filters.startDate && filters.startDate.trim()) {
    startDate = new Date(filters.startDate + 'T00:00:00.000Z');
  }

 if (filters.endDate && filters.endDate.trim()) {
    endDate = new Date(filters.endDate + 'T23:59:59.999Z');
  }
   const daysDifference = startDate && endDate ?
    Math.floor((new Date(endDate).setHours(0, 0, 0, 0) - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1 : 0;

  const exactDates: string[] = [];

  if (daysDifference > 0 && startDate && endDate) {
    const current = new Date(startDate);
    const endDateOnly = new Date(endDate);
    endDateOnly.setHours(0, 0, 0, 0);
    
    while (current <= endDateOnly) {
      exactDates.push(current.toISOString().split('T')[0]);
      current.setDate(current.getDate() + 1);
    }
  }

  const skip = (page - 1) * limit;
  const whereClause: any = {
    isWeekly: false, 
  };
  if (startDate) {
    whereClause.createdAt = {
      gte: startDate,
    };
  }
  
  const hasFilters =
    (filters.firstName && filters.firstName.trim()) ||
    (filters.lastName && filters.lastName.trim()) ||
    (filters.email && filters.email.trim()) ||
    (filters.score && filters.score.trim()) ||
    (filters.coins && filters.coins.trim()) ||
    (filters.streak && filters.streak.trim()) ||
    (daysDifference > 0);

  let dateWhereCondition: any = undefined;

   if (daysDifference > 0) {
    dateWhereCondition = {
      OR: exactDates.map(dateStr => ({
        createdAt: {
          gte: new Date(dateStr + 'T00:00:00.000Z'),
          lte: new Date(dateStr + 'T23:59:59.999Z')
        }
      }))
    };
  } else {
    if (startDate && endDate) {
      dateWhereCondition = {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      };
    } else if (startDate && !endDate) {
      dateWhereCondition = {
        createdAt: {
          gte: startDate,
        },
      };
    } else if (!startDate && endDate) {
      dateWhereCondition = {
        createdAt: {
          lte: endDate,
        },
      };
    }
  }

  let groupedResults = await prisma.mockExamResult.groupBy({
    by: ["studentId"],
    _sum: {
      score: true,
      duration: true, 
    },
    _min: {
      createdAt: true,
    },
    where: {
      isWeekly: false,
      ...(dateWhereCondition || {})
    },
    orderBy: [
      { _sum: { score: "desc" } }, 
      { _sum: { duration: "asc" } }, 
    ],
  });
  const allStudentIds = groupedResults.map((r) => r.studentId);

  let studentDetails: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
   profile?: {
     photo: string | null;
    };
  }[] = [];

  try {
    const response = await axios.post(
      `${process.env.UEST_BACKEND_URL}/uwhiz-terminated-students`,
      { applicantIds: allStudentIds },
      { headers: { "Content-Type": "application/json" } }
    );
    studentDetails = response.data;
  } catch (err) {
    console.error("Failed to fetch student details:", err);
  }

  const validStudentIds = studentDetails.map((s) => s.id);

  const filteredResults = groupedResults.filter((r) =>
    validStudentIds.includes(r.studentId)
  );

  const coinResults = await prisma.mockExamResult.groupBy({
    by: ["studentId"],
    _sum: {
      coinEarnings: true,
    },
    where: {
      studentId: {
        in: validStudentIds,
      },
      isWeekly: false,
      ...(dateWhereCondition || {})
    },
  });

  const streaks = await prisma.mockExamStreak.findMany({
    where: {
      studentId: {
        in: validStudentIds,
      },
    },
    select: {
      studentId: true,
      streakCount: true,
    },
  });

  const reactions = await prisma.dailyQuizReaction.findMany({
    where: {
      studentId: {
        in: validStudentIds,
      },
    },
    select: {
      studentId: true,
      reactionType: true,
    },
  });

  const reactionLogs = await prisma.dailyQuizReaction.groupBy({
    by: ["studentId", "reactionType"],
    _count: {
      reactionType: true,
    },
    where: {
      studentId: {
        in: validStudentIds,
      },
    },
  });

  let enrichedResults = filteredResults.map((result, index) => {
    const streak = streaks.find((s) => s.studentId === result.studentId);
    const student = studentDetails.find((s) => s.id === result.studentId);
    const coins = coinResults.find((c) => c.studentId === result.studentId);

    return {
      ...result,
      rank: index + 1,
      firstName: student?.firstName || 'Unknown',
      lastName: student?.lastName || '',
      email: student?.email || 'N/A',
      profilePhoto: student?.profile?.photo,
      coinEarnings: coins?._sum.coinEarnings ?? 0,
      streakCount: streak?.streakCount ?? 0,
      score: result._sum.score ?? 0,
    };
  });


  if (hasFilters) {
    if (filters.firstName && filters.firstName.trim()) {
      const filterTerm = filters.firstName.toLowerCase().trim();
      enrichedResults = enrichedResults.filter((student: any) =>
        student.firstName?.toLowerCase().includes(filterTerm)
      );
    }

    if (filters.lastName && filters.lastName.trim()) {
      const filterTerm = filters.lastName.toLowerCase().trim();
      enrichedResults = enrichedResults.filter((student: any) =>
        student.lastName?.toLowerCase().includes(filterTerm)
      );
    }

    if (filters.email && filters.email.trim()) {
      const filterTerm = filters.email.toLowerCase().trim();
      enrichedResults = enrichedResults.filter((student: any) =>
        student.email?.toLowerCase().includes(filterTerm)
      );
    }

    if (filters.score && filters.score.trim()) {
      const exactScore = parseInt(filters.score.trim());
      if (!isNaN(exactScore)) {
        enrichedResults = enrichedResults.filter((student: any) =>
          student.score === exactScore
        );
      }
    }

    if (filters.coins && filters.coins.trim()) {
      const exactCoins = parseInt(filters.coins.trim());
      if (!isNaN(exactCoins)) {
        enrichedResults = enrichedResults.filter((student: any) =>
          student.coinEarnings === exactCoins
        );
      }
    }

    if (filters.streak && filters.streak.trim()) {
      const exactStreak = parseInt(filters.streak.trim());
      if (!isNaN(exactStreak)) {
        enrichedResults = enrichedResults.filter((student: any) =>
          student.streakCount === exactStreak
        );
      }
    }
  }


  enrichedResults = enrichedResults.map((result, index) => ({
    ...result,
    rank: index + 1,
  }));

  const total = enrichedResults.length;
  const paginatedResults = enrichedResults.slice(skip, skip + limit);

  const badgeImages = {
    PerfectMonth: "/Perfect Month.svg",
    PerfectYear: "/Perfect Year.svg",
    DailyStreak: "/Streak.svg",
  };

  const badgeAlts = {
    PerfectMonth: "Perfect Month Badge",
    PerfectYear: "Perfect Year Badge",
    DailyStreak: "Daily Streak Badge",
  };

  const leaderboard = paginatedResults.map((result) => {
    const streakCount = result.streakCount;
    const badges = [];

    if (streakCount > 0) {
      badges.push({
        badgeType: "DailyStreak",
        badgeSrc: badgeImages.DailyStreak,
        badgeAlt: `${badgeAlts.DailyStreak} - ${streakCount} days`,
        count: streakCount,
      });
    }
    if (streakCount >= 30) {
      badges.push({
        badgeType: "PerfectMonth",
        badgeSrc: badgeImages.PerfectMonth,
        badgeAlt: badgeAlts.PerfectMonth,
      });
    }
    if (streakCount >= 365) {
      badges.push({
        badgeType: "PerfectYear",
        badgeSrc: badgeImages.PerfectYear,
        badgeAlt: badgeAlts.PerfectYear,
      });
    }

    return {
      rank: result.rank,
      studentId: result.studentId,
      score: result.score,
      coinEarnings: result.coinEarnings,
      streakCount: streakCount,
      firstName: result.firstName,
      lastName: result.lastName,
      email: result.email,
      profilePhoto: result.profilePhoto,
      badge: {
        streakCount,
        badges,
      },
    };
  });

  const reactionData: {
    [studentId: string]: {
      reaction: string | null;
      counts: {
        thumbsup: number;
        whistle: number;
        party: number;
        clap: number;
      };
    };
  } = {};

  validStudentIds.forEach((studentId) => {
    const reaction = reactions.find((r) => r.studentId === studentId);
    const counts = { thumbsup: 0, whistle: 0, party: 0, clap: 0 };

    reactionLogs
      .filter((log) => log.studentId === studentId)
      .forEach((log) => {
        counts[log.reactionType as keyof typeof counts] =
          log._count.reactionType;
      });

    reactionData[studentId] = {
      reaction: reaction?.reactionType || null,
      counts,
    };
  });

  return {
    data: leaderboard,
    total,
    reactions: reactionData,
  };
}

export async function calculatePreviousDayTop3() {
  try {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);
    const endOfYesterday = new Date(yesterday);
    endOfYesterday.setHours(23, 59, 59, 999);

    const groupedResults = await prisma.mockExamResult.groupBy({
      by: ["studentId"],
      _sum: {
        score: true,
        duration: true, 
      },
      where: {
        createdAt: {
          gte: yesterday,
          lte: endOfYesterday,
        },
        isWeekly: false,
      },
      orderBy: [
        { _sum: { score: "desc" } }, 
        { _sum: { duration: "asc" } }, 
      ],
      take: 3, 
    });

    const allStudentIds = groupedResults.map((r) => r.studentId);

    let studentDetails: {
      id: string;
      firstName: string;
      lastName: string;
      profile: { photo: string | null };
    }[] = [];
    try {
      const response = await axios.post(
        `${process.env.UEST_BACKEND_URL}/uwhiz-terminated-students`,
        { applicantIds: allStudentIds },
        { headers: { "Content-Type": "application/json" } }
      );
      studentDetails = response.data;
    } catch (err) {
      console.error("Failed to fetch student details:", err);
    }

    const validStudentIds = studentDetails.map((s) => s.id);

    const streaks = await prisma.mockExamStreak.findMany({
      where: {
        studentId: {
          in: validStudentIds,
        },
      },
      select: {
        studentId: true,
        streakCount: true,
      },
    });

    const top3 = groupedResults
      .filter((r) => validStudentIds.includes(r.studentId))
      .map((result, index) => {
        const student = studentDetails.find((s) => s.id === result.studentId);
        const streak = streaks.find((s) => s.studentId === result.studentId);
        return {
          rank: index + 1,
          firstName: student?.firstName || "Unknown",
          lastName: student?.lastName || "",
          profilePhoto: student?.profile?.photo || null,
          score: result._sum.score || 0,
          streakCount: streak?.streakCount || 0,
          duration: result._sum.duration || 0, 
        };
      });

    cache.set("previous_day_top3", top3, 86400);

    // Send notifications to all students about the top 3
    if (top3.length > 0) {
      try {
        await axios.post(`${process.env.UEST_BACKEND_URL}/notifications/create-leaderboard`, {
          top3Students: top3
        });
        console.log("Leaderboard notifications sent successfully");
      } catch (notificationError) {
        console.error("Failed to send leaderboard notifications:", notificationError);
      }
    }

    return top3;
  } catch (error) {
    console.error("Error calculating previous day top 3:", error);
    cache.set("previous_day_top3", [], 86400);
    return [];
  }
}

export async function getPreviousDayTop3() {
  let top3 = cache.get("previous_day_top3") as any[] | undefined;
  if (top3 === undefined) {
    top3 = await calculatePreviousDayTop3();
  }
  return top3;
}