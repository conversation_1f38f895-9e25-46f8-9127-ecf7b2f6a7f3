import prisma from '../config/prismaClient';
import axios from 'axios';

// Fetch exam applicants data for export
export const fetchExamApplicationsForExport = async (examId: number) => {
  // First, get the exam details
  const exam = await prisma.exam.findUnique({
    where: { id: examId },
    select: {
      id: true,
      exam_name: true,
      exam_type: true,
    }
  });

  if (!exam) {
    throw new Error('Exam not found');
  }

  // Get all applications for this exam
  const applications = await prisma.examApplication.findMany({
    where: { examId },
    orderBy: { createdAt: 'desc' },
    select: {
      id: true,
      applicantId: true,
      createdAt: true,
    }
  });

  if (applications.length === 0) {
    return [];
  }

  // Get applicant IDs
  const applicantIds = applications.map(app => app.applicantId);

  // Fetch user details from external service
  const response = await axios.post(`${process.env.UEST_BACKEND_URL}/uwhizApplicant/get-user-details`, {
    examType: exam.exam_type,
    applicantIds,
  });

  // Combine application data with user details
  const exportData = response.data.map((applicant: any, index: number) => {
    const application = applications[index];

    // Combine first name and last name to create full name
    let name = 'N/A';

    // Try to get first name and last name
    const firstName = applicant.firstName || applicant.first_name || applicant.fname || '';
    const lastName = applicant.lastName || applicant.last_name || applicant.lname || '';

    // If both first and last name exist, combine them
    if (firstName && lastName) {
      name = `${firstName} ${lastName}`.trim();
    }
    // If only first name exists
    else if (firstName) {
      name = firstName.trim();
    }
    else if (lastName) {
      name = lastName.trim();
    }
    // Try other possible full name fields
    else {
      name = 'N/A';
    }

    const email = applicant.email ||
                  applicant.emailAddress ||
                  applicant.email_address ||
                  'N/A';

    return {
      'Name': name,
      'Email': email,
      'Applied At': new Date(application.createdAt).toLocaleString('en-IN'),
    };
  });

  return exportData;
};

// Fetch question bank data for export
export const fetchQuestionBankForExport = async (filters: {
  medium?: string;
  standard?: string;
  level?: string;
  subject?: string;
} = {}) => {
  const where: any = {};
  if (filters.medium) where.medium = filters.medium;
  if (filters.standard) where.standard = filters.standard;
  if (filters.level) where.level = filters.level;
  if (filters.subject) where.subject = filters.subject;

  // Get all questions without pagination for export
  const questions = await prisma.questionBank.findMany({
    where,
    orderBy: { createdAt: 'desc' },
    select: {
      id: true,
      question: true,
      optionOne: true,
      optionTwo: true,
      optionThree: true,
      optionFour: true,
      correctAnswer: true,
      medium: true,
      standard: true,
      subject: true,
      level: true,
      classID: true,
      createdAt: true,
    }
  });

  // Format data for Excel export
  const exportData = questions.map((question, index) => ({
    'Sr. No.': index + 1,
    'Question': question.question,
    'Option A': question.optionOne,
    'Option B': question.optionTwo,
    'Option C': question.optionThree,
    'Option D': question.optionFour,
    'Correct Answer': question.correctAnswer,
    'Medium': question.medium,
    'Standard': question.standard,
    'Subject': question.subject,
    'Level': question.level,
    'Class ID': question.classID || 'N/A',
    'Created At': new Date(question.createdAt || '').toLocaleString('en-IN'),
  }));

  return exportData;
};
export const fetchStudentResultsForExport = async (examId: number) => {
  const exam = await prisma.exam.findUnique({
    where: { id: examId },
    select: {
      id: true,
      exam_name: true,
      exam_type: true,
    }
  });

  if (!exam) {
    throw new Error('Exam not found');
  }
  const examAnswers = await prisma.saveExamAnswer.findMany({
    where: { examId },
    include: {
      questionBank: {
        select: {
          correctAnswer: true,
        }
      }
    }
  });

  if (examAnswers.length === 0) {
    return [];
  }
  const studentStats = examAnswers.reduce((acc, answer) => {
    const studentId = answer.studentId;
    
    if (!acc[studentId]) {
      acc[studentId] = {
        studentId,
        totalQuestions: 0,
        correctAnswers: 0,
        attempts: 0,
      };
    }
    acc[studentId].totalQuestions++;
    if (answer.selectedAns) {
      acc[studentId].attempts++
      if (answer.selectedAns === answer.questionBank.correctAnswer) {
        acc[studentId].correctAnswers++;
      }
    }

    return acc;
  }, {} as Record<string, any>);
  const studentIds = Object.keys(studentStats);

  if (studentIds.length === 0) {
    return [];
  }
  const response = await axios.post(`${process.env.UEST_BACKEND_URL}/uwhiz-terminated-students`, {
    examType: exam.exam_type,
    applicantIds: studentIds,
  });
    const studentsWithResults = response.data.map((student: any) => {
    const stats = studentStats[student.id || student.studentId || student.applicantId];
    const firstName = student.firstName || student.first_name || student.fname || '';
    const lastName = student.lastName || student.last_name || student.lname || '';
    const email = student.email || student.emailAddress || student.email_address || 'N/A';
    const contact = student.contact || student.contactNumber || student.Phone || 'N/A';
    const accuracy = stats.attempts > 0 ? 
      ((stats.correctAnswers / stats.attempts) * 100).toFixed(2) + '%' : 
      '0.00%';

    return {
      studentId: stats.studentId,
      firstName: firstName || 'N/A',
      lastName: lastName || 'N/A', 
      contact:contact||'N/A',
      email,
      correctAnswers: stats.correctAnswers,
      attempts: stats.attempts,
      totalQuestions: stats.totalQuestions,
      accuracy,
      accuracyScore: stats.attempts > 0 ? (stats.correctAnswers / stats.attempts) * 100 : 0
    };
  });
  studentsWithResults.sort((a: { accuracyScore: number; }, b: { accuracyScore: number; }) => b.accuracyScore - a.accuracyScore);

  const exportData = studentsWithResults.map((student: { firstName: any; lastName: any; email: any; correctAnswers: any; attempts: any; totalQuestions: any; accuracy: any; contact:any }, index: number) => ({
    'Rank': index + 1,
    'First Name': student.firstName,
    'Last Name': student.lastName,
    'Student Email': student.email,
    'Student contact': student.contact,
    'Correct Answers': student.correctAnswers,
    'Attempts': student.attempts,
    'Total Questions': student.totalQuestions,
    'Accuracy': student.accuracy,
  }));

  return exportData;
};




