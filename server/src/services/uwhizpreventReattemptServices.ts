const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

export const hasUserAttemptedExam = async (studentId: any, examId: any) => {
  try {
    const examIdNum = Number(examId);

    const exam = await prisma.exam.findUnique({
      where: {
        id: examIdNum,
      },
      select: {
        total_questions: true,
      },
    });

    if (!exam) {
      console.error(`Exam with ID ${examId} not found`);
      return false; 
    }

    const attemptedAnswersCount = await prisma.saveExamAnswer.count({
      where: {
        studentId,
        examId: examIdNum,
      },
    });

    const terminationCount = await prisma.quizTemination.count({
      where: {
        applicantId: studentId,
        examId: examIdNum,
      },
    });

    return attemptedAnswersCount >= exam.total_questions || terminationCount >= 3;
  } catch (error) {
    console.error('Error checking exam attempt:', error);
    return false; 
  }
};


export const countTerminations = async (studentId: any, examId: any) => {
 try{
    const terminationCount = await prisma.quizTemination.count({
      where: {
        applicantId: studentId,
        examId: Number(examId),
      },
    });

    return terminationCount;
  } catch (error) {
    console.error('Error checking exam attempt:', error);
    return false; 
  }
}