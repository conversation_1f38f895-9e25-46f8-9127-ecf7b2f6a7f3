import { Response } from 'express';

export const sendSuccess = (res: Response, data: unknown, message = 'Success', status = 200) => {
  return res.status(status).json({ success: true, message, data });
};

export const sendError = (res: Response, error: any, status = 500) => {
  const message = typeof error === 'string' ? error : error.message || 'Something went wrong';
  return res.status(status).json({ success: false, message });
};
