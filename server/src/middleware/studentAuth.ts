import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { sendError } from '../utils/response';
import dotenv from 'dotenv';

declare global {
  namespace Express {
    interface Request {
      student?: {
        id: string;
        contact: string;
      };
    }
  }
}

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'secret123';


export const studentAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      sendError(res, 'Authentication required', 401);
      return;
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      sendError(res, 'Authentication required', 401);
      return;
    }

    const decoded = jwt.verify(token, JWT_SECRET) as { id: string; contactNo: string };
    if (!decoded || !decoded.id) {
      sendError(res, 'Invalid token', 401);
      return;
    }

    req.student = {
      id: decoded.id,
      contact: decoded.contactNo ?? ''
    };

    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    sendError(res, 'Authentication failed', 401);
  }
};