import { Request, Response, NextFunction } from "express";
import { z, AnyZodObject } from "zod";

export const validateSchema = (schema: AnyZodObject) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      await schema.parseAsync(req.body);
      next();
    } catch (error) {
      res.status(400).json({
        success: false,
        error: "Validation failed",
        details: error instanceof z.ZodError ? error.errors : error,
      });
    }
  };
};