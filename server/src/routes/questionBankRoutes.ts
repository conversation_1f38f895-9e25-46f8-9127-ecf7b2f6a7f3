import {Router} from 'express'
import {createNewQuestion,getAllQuestions,deleteQuestions,updateQuestion,handleUploadExcel, deleteManyQuestions} from '../controller/questionBankController'
import { validateSchema } from '../middleware/validateSchema';
import { createQuestionSchema,upadateQuestionSchema } from '../validations/questionBankValidation';
import { authClientMiddleware } from '../middleware/clientAuth';
import { uploadExcelWithErrorHandling } from '../middleware/uploadMiddleware';

const questionBankRoutes = Router();

questionBankRoutes.get('/',authClientMiddleware,getAllQuestions);
questionBankRoutes.post('/',authClientMiddleware,validateSchema(createQuestionSchema),createNewQuestion);
questionBankRoutes.post('/upload-excel', uploadExcelWithErrorHandling, handleUploadExcel);
questionBankRoutes.put('/:id',authClientMiddleware,validateSchema(upadateQuestionSchema),updateQuestion);
questionBankRoutes.delete('/:id',authClientMiddleware,deleteQuestions);
questionBankRoutes.delete('/bulk/delete', deleteManyQuestions);

export default questionBankRoutes;