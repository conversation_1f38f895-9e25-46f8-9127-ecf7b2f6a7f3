import { Router } from 'express';
import { createNewMockExamController,deleteMockExamController, getAllMockExamQuestionController, updateMockExamController,deleteManyMockExamController, importExcelController } from '../controller/mockExamQuestionBankController';
import { uploadExcelWithErrorHandling } from '../middleware/uploadMiddleware';

 const mockExamQuestionBank = Router();

mockExamQuestionBank.get('/', getAllMockExamQuestionController);
mockExamQuestionBank.post('/', createNewMockExamController);
mockExamQuestionBank.put('/:id', updateMockExamController);
mockExamQuestionBank.delete('/:id', deleteMockExamController);
mockExamQuestionBank.delete('/bulk/delete', deleteManyMockExamController);
mockExamQuestionBank.post('/import',uploadExcelWithErrorHandling, importExcelController);

export default mockExamQuestionBank;