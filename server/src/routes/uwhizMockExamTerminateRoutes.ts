import { Router } from "express";
import {addQuizTeminationController,countTerminationsController, getStudentTerminationLogsController} from  "../controller/uwhizMockExamTeminateController";

const mockExamTeminationRouter = Router();

mockExamTeminationRouter.post('/',addQuizTeminationController);
mockExamTeminationRouter.get('/count',countTerminationsController);
mockExamTeminationRouter.get('/:studentId',getStudentTerminationLogsController);

export default mockExamTeminationRouter; 