import {Router} from 'express'
import {addSubjectPrefrenceController,getSubjectPrefrencesController,deleteSubjectPrefrences} from '../controller/subjectPrefrencesController'
import { authClientMiddleware } from '../middleware/clientAuth';

const subjectPrefrenceRoutes = Router();

subjectPrefrenceRoutes.get('/:examId',authClientMiddleware,getSubjectPrefrencesController);
subjectPrefrenceRoutes.post('/',authClientMiddleware, addSubjectPrefrenceController);
subjectPrefrenceRoutes.delete('/:id',authClientMiddleware,deleteSubjectPrefrences);

export default subjectPrefrenceRoutes;