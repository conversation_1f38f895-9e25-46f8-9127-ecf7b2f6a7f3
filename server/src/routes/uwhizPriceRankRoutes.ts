import { authClientMiddleware } from "../middleware/clientAuth";
import {
  createPriceRankController,
  deletePriceRankController,
  getPriceRanksController,
  updatePriceRankController,
} from "../controller/uwhizPriceRankController";

import { Router } from "express";

const uwhizPriceRank = Router();
uwhizPriceRank.post("/",authClientMiddleware, createPriceRankController);
uwhizPriceRank.get("/:examId", authClientMiddleware,getPriceRanksController);
uwhizPriceRank.put("/:id",authClientMiddleware, updatePriceRankController);
uwhizPriceRank.delete("/:id",authClientMiddleware, deletePriceRankController);

export default uwhizPriceRank;
