import { Router } from 'express';
import { saveMockExamResultController, getMockExamCombinedController } from '../controller/mockExamResultController';
import { studentAuthMiddleware } from '../middleware/studentAuth';
import { authClientMiddleware } from '../middleware/clientAuth';
 const mockExamResultRoutes = Router();

mockExamResultRoutes.get('/:studentId', authClientMiddleware, getMockExamCombinedController);
mockExamResultRoutes.post('/', studentAuthMiddleware, saveMockExamResultController);
mockExamResultRoutes.get('/:studentId', studentAuthMiddleware, getMockExamCombinedController);

export default mockExamResultRoutes;