import { Router } from "express";
import {addQuizTeminationController,getStudentTerminationLogsController,getTerminatedStudentsController} from  "../controller/quizTeminationController";

const quizTeminationRouter = Router();

quizTeminationRouter.post('/',addQuizTeminationController);
quizTeminationRouter.get('/:examId',getTerminatedStudentsController);
quizTeminationRouter.get('/:examId/:studentId', getStudentTerminationLogsController);


export default quizTeminationRouter; 