import express from "express";
import {
  triggerDailyQuizStreakNotifications,
  getStreakStatisticsController,
  getNotificationSystemStatus
} from "../controller/dailyQuizStreakNotificationController";

const dailyQuizStreakNotificationRoutes = express.Router();

dailyQuizStreakNotificationRoutes.post("/trigger", triggerDailyQuizStreakNotifications);

dailyQuizStreakNotificationRoutes.get("/statistics", getStreakStatisticsController);

dailyQuizStreakNotificationRoutes.get("/status", getNotificationSystemStatus);

export default dailyQuizStreakNotificationRoutes;